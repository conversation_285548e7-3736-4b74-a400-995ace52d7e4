# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-12
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   login_business.py
@Software   :   PyCharm
"""

import random
import string

import allure
from jmespath import search
from neox_test_common import UIA, logger

from neox_test_scenarios import (
    com_click_login_btn,
    com_open_yakushi_app,
    com_show_desktop,
    com_write_acc_info,
)


def perform_login_flow(config: dict, use_correct_credentials: bool = True) -> None:
    """
    Perform complete login flow with either correct or incorrect credentials

    Args:
        config (dict): Test configuration data
        use_correct_credentials (bool): True for correct credentials, False for random incorrect ones
    """
    # Step 1: Show desktop
    with allure.step("显示桌面"):
        com_show_desktop()

    # Step 2: Open Yakushi client
    with allure.step("打开Yakushi客户端"):
        assert com_open_yakushi_app(config)

    # Step 3: Input username
    if use_correct_credentials:
        username = search("yakushi.modules.login.account", config)
        step_desc = "在账户信息文本框中输入正确的账户名"
    else:
        username = "".join(
            random.choice(string.ascii_letters + string.digits) for _ in range(8)
        )
        step_desc = "在账户信息文本框中输入错误的账户名"

    with allure.step(step_desc):
        user_data = {
            "control": search("yakushi.modules.login.control.box.user", config),
            "log": {
                "debug": "账户信息文本框窗口",
                "info": f"< Step 1 :: {step_desc} >",
            },
            "text": username,
        }
        assert com_write_acc_info(user_data)

    # Step 4: Input password
    if use_correct_credentials:
        password = search("yakushi.modules.login.password", config)
        step_desc = "在账户密码文本框中输入正确的密码"
    else:
        password = "".join(
            random.choice(string.ascii_letters + string.digits) for _ in range(8)
        )
        step_desc = "在账户密码文本框中输入错误的密码"

    with allure.step(step_desc):
        pwd_data = {
            "control": search("yakushi.modules.login.control.box.password", config),
            "log": {
                "debug": "账户密码文本框窗口",
                "info": f"< Step 2 :: {step_desc} >",
            },
            "text": password,
        }
        assert com_write_acc_info(pwd_data)

    # Step 5: Click login button
    with allure.step("点击登录按钮"):
        btn_data = {
            "control": search("yakushi.modules.login.control.btn.login", config),
            "log": {"debug": "登录按钮", "info": "< Step 3 :: 点击登录按钮 >"},
        }
        assert com_click_login_btn(btn_data)


def verify_login_success(config: dict) -> None:
    """
    Verify successful login by checking for application windows
    """
    with allure.step("登录成功并在跳转后的页面中找到断言标志"):
        logger.info("< Step 4 :: 验证是否为客户端安装后首次登录...检测中... >")
        cert_installed_window = UIA.WindowControl(
            Name="", ClassName="#32770", searchDepth=2, timeout=20
        )
        if cert_installed_window is not None:
            logger.info("< Step 4 :: 确认为客户端安装后首次登录 >")
            UIA.setWindowActive(cert_installed_window)
            cert_installed_confirm_button = UIA.ButtonControl(
                parent=cert_installed_window, Name="确定"
            )
            _ = UIA.clickButton(button=cert_installed_confirm_button)
            logger.info("< Step 4 :: 点击证书安装完成弹窗的确认按钮 >")
            logger.debug(
                f"证书安装完成弹窗的确认按钮-RECT：{cert_installed_confirm_button.BoundingRectangle}"
            )
        else:
            logger.info("< Step 4 :: 确认非客户端安装后首次登录，无需安装证书 >")

        float_window = UIA.WindowControl(
            Name="薬師丸賢太",
            ClassName="Window",
            AutomationId=search("yakushi.modules.common.window.float.auto_id", config),
            timeout=3,
        )
        logger.debug(f"浮动窗口-RECT：{float_window.BoundingRectangle}")
        main_window = UIA.WindowControl(
            Name="薬師丸賢太",
            ClassName="Window",
            AutomationId=search("yakushi.modules.common.window.main.auto_id", config),
            timeout=3,
        )
        logger.debug(f"主页窗口-RECT：{main_window.BoundingRectangle}")

        assert all((float_window, main_window))


def verify_login_failure() -> None:
    """
    Verify login failure by checking for error dialog and dismissing it
    """
    with allure.step("登录失败并在跳出的错误信息弹窗中点击确认"):
        logger.info("< Step 4 :: 登录失败并跳出错误信息弹窗 >")
        login_failed_confirm_window = UIA.WindowControl(
            Name="", ClassName="#32770", searchDepth=2, timeout=10
        )
        if login_failed_confirm_window is not None:
            logger.debug(
                f"登录失败错误信息弹窗-RECT：{login_failed_confirm_window.BoundingRectangle}"
            )
            login_failed_confirm_button = UIA.ButtonControl(
                parent=login_failed_confirm_window, Name="确定"
            )
            logger.debug(
                f"登录失败错误信息弹窗的确认按钮-RECT：{login_failed_confirm_button.BoundingRectangle}"
            )
            status_click = UIA.clickButton(button=login_failed_confirm_button)
            logger.info("< Step 4 :: 点击登录失败错误信息弹窗的确认按钮 >")
            assert status_click
        else:
            assert False
