#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-19
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   common.py
@Software   :   PyCharm
"""

from typing import Dict, Optional

from jmespath import search
from neox_test_common import UIA, logger
from uiautomation import WindowControl

# Constants
YAKUSHI_WINDOW_NAME = "薬師丸賢太"
YAKUSHI_WINDOW_CLASS = "Window"
DEFAULT_SEARCH_DEPTH = 1
DEFAULT_TIMEOUT = 3


def com_show_desktop() -> None:
    """
    Show the desktop by minimizing all windows.

    This function triggers the Windows "Show Desktop" functionality,
    which minimizes all open windows and displays the desktop. This
    is useful for clearing the screen before starting UI automation
    tests or when needing to access desktop elements.

    Example:
        >>> com_show_desktop()
        # All windows are minimized and desktop is shown

    Note:
        This function uses the Windows key + D keyboard shortcut
        to show the desktop.
    """
    # 执行显示桌面操作
    UIA.showDesktop()
    logger.info("< Step Common :: 显示桌面 >")


def com_open_yakushi_app(config: Dict) -> Optional[WindowControl]:
    """
    Check if the Yakushi client is running and start it if necessary.

    This function first checks if the Yakushi client application is already
    running by looking for its login window. If the client is not running,
    it attempts to start the application using the executable path specified
    in the configuration.

    Args:
        config: Dictionary containing test configuration parameters, including
               the Yakushi executable path and timeout settings

    Returns:
        WindowControl object representing the Yakushi client's login window
        if successfully found or started, None if the client cannot be started

    Example:
        >>> config = {
        ...     "yakushi": {
        ...         "locate": {"exe_abs_path": "C:/Program Files/Yakushi/yakushi.exe"},
        ...         "modules": {"login": {"check_cert_installed_timeout": 30}}
        ...     }
        ... }
        >>> window = com_open_yakushi_app(config)
        >>> if window:
        ...     print(f"Yakushi client window found: {window.ProcessId}")

    Note:
        The function logs detailed information about the client startup process
        and window detection for debugging purposes.
    """
    # 首先检查 Yakushi 客户端是否已经在运行
    login_window = UIA.WindowControl(
        Name=YAKUSHI_WINDOW_NAME,
        ClassName=YAKUSHI_WINDOW_CLASS,
        searchDepth=DEFAULT_SEARCH_DEPTH,
        timeout=DEFAULT_TIMEOUT,
    )
    # 检查 Yakushi 客户端是否已经在运行
    if login_window is None:
        # 客户端未运行，尝试启动
        logger.info("< Step Common :: Yakushi客户端未启动 >")

        # 从配置中获取可执行文件路径
        exe_abs_path = search("yakushi.locate.exe_abs_path", config)
        if not exe_abs_path:
            logger.error("配置中未找到 Yakushi 可执行文件路径")
            return None

        # 尝试启动 Yakushi 客户端
        if UIA.openApplication(app=exe_abs_path):
            logger.info("< Step Common :: 打开Yakushi客户端 >")

            # 获取证书安装检查超时时间
            check_cert_installed_timeout = (
                search("yakushi.modules.login.check_cert_installed_timeout", config)
                or 30
            )  # 默认30秒超时

            # 等待登录窗口出现
            login_window = UIA.WindowControl(
                Name=YAKUSHI_WINDOW_NAME,
                ClassName=YAKUSHI_WINDOW_CLASS,
                searchDepth=DEFAULT_SEARCH_DEPTH,
                timeout=check_cert_installed_timeout,
            )

            # 记录客户端进程和窗口信息
            if login_window:
                logger.info(f"Yakushi客户端进程号：[ {login_window.ProcessId} ]")
                logger.info(
                    f"Yakushi客户端登录窗口-RECT：[ {login_window.BoundingRectangle} ]"
                )
            else:
                logger.error("启动 Yakushi 客户端后未能找到登录窗口")
        else:
            logger.error(f"无法启动 Yakushi 客户端：{exe_abs_path}")
            return None
    else:
        # 客户端已经在运行
        logger.info("< Step Common :: Yakushi客户端已启动 >")

    return login_window


def com_write_acc_info(data: dict) -> bool:
    """
    This function is used to input account information into the Yakushi client.

    Parameters:
        data (dict): A dictionary containing the following keys:
            control (dict): A dictionary containing the following keys:
                class_name (str): The class name of the edit control.
                auto_id (str): The automation ID of the edit control.
            text (str): The text to be entered into the edit control.
            log (dict): A dictionary containing the following keys:
                info (str): The information to be logged when the function completes successfully.
                debug (str): The debug information to be logged when the function completes successfully.

    Returns:
        bool: A boolean value indicating whether the account information was successfully entered.

    """
    control, text, log = data["control"], data["text"], data["log"]

    login_window = UIA.WindowControl(
        Name="薬師丸賢太", ClassName="Window", searchDepth=1
    )
    status_input = False
    if login_window is not None:
        UIA.setWindowActive(login_window)
        text_box = UIA.EditControl(
            parent=login_window,
            ClassName=control["class_name"],
            AutomationId=control["auto_id"],
        )
        _ = UIA.inputEditControlText(editControl=text_box, text="")
        status_input = UIA.inputEditControlText(editControl=text_box, text=text)
        logger.info(f"{log['info']}")
        logger.info(f"{log['debug']}-RECT：[ {text_box.BoundingRectangle} ]")

    return status_input


def com_click_login_btn(data: dict) -> bool:
    """
    This function is used to click the login button in the Yakushi client.

    Parameters:
        data (dict): A dictionary containing the following keys:
            control (dict): A dictionary containing the following keys:
                btn_name (str): The name of the login button.
            log (dict): A dictionary containing the following keys:
                info (str): The information to be logged when the function completes successfully.
                debug (str): The debug information to be logged when the function completes successfully.

    Returns:
        bool: A boolean value indicating whether the login button was successfully clicked.

    """
    control, log = data["control"], data["log"]

    login_window = UIA.WindowControl(
        Name="薬師丸賢太", ClassName="Window", searchDepth=1
    )
    status_click = False
    if login_window is not None:
        UIA.setWindowActive(login_window)
        login_button = UIA.ButtonControl(parent=login_window, Name=control["btn_name"])
        status_click = UIA.clickButton(button=login_button)
        logger.info(f"{log['info']}")
        logger.info(f"{log['debug']}-RECT：[ {login_button.BoundingRectangle} ]")

    return status_click


def com_get_yakushi_window(
    auto_id: str, searchDepth: int, timeout: int = 5
) -> WindowControl | None:
    """
    This function is used to open the Yakushi client's window.
    """
    return UIA.WindowControl(
        Name="薬師丸賢太",
        ClassName="Window",
        AutomationId=auto_id,
        searchDepth=searchDepth,
        timeout=timeout,
    )


def com_open_homepage_from_float_window(data: dict) -> bool:
    """
    This function is used to open the homepage from the Yakushi client's float window.

    Parameters:
        data (dict): A dictionary containing the following keys:
            float_window (dict): A dictionary containing the following keys:
                auto_id (str): The automation ID of the float window.
            main_window (dict): A dictionary containing the following keys:
                auto_id (str): The automation ID of the main window.
            main_window_button (dict): A dictionary containing the following keys:
                auto_id (str): The automation ID of the button that shows the main window.
            float_logo_button (dict): A dictionary containing the following keys:
                auto_id (str): The automation ID of the button that closes the float window.

    Returns:
        bool: A boolean value indicating whether the homepage was successfully opened.

    """
    fw, mw, mwb, flb = (
        data["float_window"],
        data["main_window"],
        data["main_window_button"],
        data["float_logo_button"],
    )
    float_window = com_get_yakushi_window(
        auto_id=fw["auto_id"], searchDepth=1, timeout=3
    )
    main_window = com_get_yakushi_window(
        auto_id=mw["auto_id"], searchDepth=1, timeout=3
    )
    logger.info("< Step Common :: 查找浮动窗口与主页窗口 >")
    logger.info(f"浮动窗口-RECT：[ {float_window.BoundingRectangle} ]")

    if main_window is None:
        # The main window is not open, so try to show it
        logger.info("< Step Common :: 未找到主页窗口 >")
        show_main_window_button = UIA.ButtonControl(
            parent=float_window, Name="", AutomationId=mwb["auto_id"]
        )
        _ = UIA.clickButton(button=show_main_window_button, waitTime=1)
        logger.info("< Step Common :: 查找并点击浮动窗口中的打开主页窗口按钮 >")
        main_window = com_get_yakushi_window(
            auto_id=mw["auto_id"], searchDepth=1, timeout=1
        )
        logger.info("< Step Common :: 再次查找主页窗口 >")
        if main_window is None:
            # The main window is still not open, so try to click the float window and show it again
            logger.info("< Step Common :: 未找到主页窗口 >")
            float_logo_button = UIA.ButtonControl(
                parent=float_window, Name="", AutomationId=flb["auto_id"]
            )
            _ = UIA.clickButton(button=float_logo_button, waitTime=1)
            logger.info("< Step Common :: 查找并点击浮动窗口中的LOGO，展开浮动窗口 >")
            _ = UIA.clickButton(button=show_main_window_button, waitTime=1)
            logger.info("< Step Common :: 再次点击浮动窗口中的打开主页窗口按钮 >")
            main_window = com_get_yakushi_window(
                auto_id=mw["auto_id"], searchDepth=1, timeout=1
            )

    logger.info(f"主页窗口-RECT：[ {main_window.BoundingRectangle} ]")

    return UIA.setWindowActive(main_window)
