[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "neox-test-scenarios"
version = "0.0.1"
description = "The development kit of NeoX for automation testing (pytest-bdd scenarios lib)"
requires-python = ">=3.10"
dependencies = [
    "allure-pytest>=2.14.2",
    "jmespath>=1.0.1",
    "pytest-bdd>=8.1.0",
    "neox_test_common>=0.0.1",
    "neox_test_win>=0.0.1",
    "uiautomation>=2.0.28",
]
authors = [{ name = "Kuno Lu", email = "<EMAIL>" }]
readme = "README.md"
license = { text = "MIT License" }
classifiers = [
    "Development Status :: 4 - Beta",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Operating System :: OS Independent",
]

[project.urls]
Repository = "https://bitbucket.org/neoxinc/testing/src/main/"
