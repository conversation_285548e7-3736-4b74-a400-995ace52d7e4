# Testing

NeoX testing repo.



## Automation

NeoX automation.

### Installation

```powershell
### 确保已经安装了python虚拟环境！！！（conda, virtualenv...）

cd testing\Automation

## 安装依赖包
# 安装自动化测试工程通用依赖包
pip install -r requirements_auto_common.txt

# 安装自动化测试工程项目专用依赖包（示例）
cd .\Windows\KENTA
pip install -r requirements.txt
```

### Dir-structure

```powershell
~\TESTING\AUTOMATION
│   README.md
│   requirements_auto_common.txt
│   
└───Windows
    └───KENTA
        │   conftest.py
        │   pytest.ini
        │   README.md
        │   requirements.txt
        │   runall.py
        │
        └───...
```

### Docs

Pls read documents:

- [Automation Specification Document](Automation/README.md)

## Performance

### Locust

[![license](https://img.shields.io/github/license/locustio/locust.svg)](https://github.com/locustio/locust/blob/master/LICENSE)
[![PyPI](https://img.shields.io/pypi/v/locust.svg)](https://pypi.org/project/locust/)
[![PyPI](https://img.shields.io/pypi/pyversions/locust.svg)](https://pypi.org/project/locust/)

Locust is an easy to use, scriptable and scalable performance testing tool. You define the behaviour of your users in regular Python code, instead of being constrained by a UI or domain specific language that only pretends to be real code. This makes Locust infinitely expandable and very developer friendly.

#### Links

* Website: [locust.io](https://locust.io)
* Documentation: [docs.locust.io](https://docs.locust.io)

#### Installation

```bash
# 自行选择以下格式的 requirements.txt 版本安装，一般用最新的即可
cd Performance/Locust/env/requirements && pip install -r locust_vx.x.x_requirements.txt

# 同时按需安装以下依赖
# 本地开发环境：
cd Performance/Locust/env/requirements && pip install -r locust_dev_requirements.txt
# 远程环境：（即发压机）
cd Performance/Locust/env/requirements && pip install -r locust_neox_requirements.txt
```

#### Dir-structure

```bash
Locust
├───env
├── env
│   ├── Dockerfile
│   ├── .env
│   ├── lib
│   │   ├── locust_common-*.whl
│   │   └── neox_locust-*.whl
│   ├── requirements
│   │   ├── locust_dev_requirements.txt
│   │   ├── locust_neox_requirements.txt
│   │   └── locust_v2.32.5_requirements.txt
│   └── timescaledb
│       ├── docker-compose-timescaledb.yml
│       └── sql
│           ├── 01_timescale_schema_locust-plugins.sql
│           └── 02_zz_hypertable_locust-plugins.sql
│
└───scenarios
    │   BackendDBConfig.env
    │
    └───Sample
        │   locust_worker_param_filename.csv
        │   README.md
        │   start_test.sh
        │
        ├───common
        │       *.py
        │       __init__.py
        │
        ├───conf
        │       locust.conf
        │       locust_master.conf
        │       locust_worker.conf
        │
        ├───locustfiles
        │   ├───examples_locust
        │   │       *.py
        │   │
        │   ├───examples_locust-plugins
        │   │       *.py
        │   │
        │   └───examples_self_experience
        │           *.py
        │
        ├───log
        │       LOGS_FILE
        │
        ├───paramlist
        │       TXT_OR_CSV_PARAM_FILE
        │
        └───report
                REPORT_IN_HERE
```

## Tools

NeoX tool set for testing.

### NeoXHelper

#### Installation

```powershell
### 开发环境安装说明（确保已经安装了python虚拟环境！！！如：conda, virtualenv...）

cd testing\Tools\NeoXHelper

## 安装依赖包
pip install -r requirements.txt
```

#### Dir-structure

```powershell
~\TESTING\TOOLS\NEOXHELPER
│   config.toml
│   neox.py
│   neox.spec
│   README.md
│   requirements.txt
│   
├───bin
│   │   SetDpi.exe
│   
├───common
│   │   clog.py
│   │   configuration.py
│   │   context.py
│   │   mapping.py
│   │   powershell.py
│   │   variables.py
│   │   __init__.py 
│
├───scopes
│   │   replica.py
│   │   secret.py
│   │   windows.py
│   │   __init__.py
│
└───templates
    │   template_config.toml
```

#### Usage

Pls read [README.md](Tools/NeoXHelper/README.md).

### NeoXMetrics

A CLI tools which writing some metrics into prometheus.

#### Usage

Pls read [README.md](Tools/NeoXMetrics/README.md).
