# NeoXBlank

A HTTP Server which checking prescriptions if blank.

# Setup

```bash
cd testing/Tools/NeoXBlank

# Setup python environment
pip install -r requirements.txt
```

# Usage

## Run Server

```bash
python figure.py

# 或者后台执行
nohup python path/to/figure.py > output.log 2>&1 &
```

## Check Blank

### Request

```bash
curl -X POST http://localhost:6999/check_blank -H "Content-Type: application/json" -d '{"image_path": "path/to/image.png"}'
```

### Response

```json
{
    "result": 0.5
}
```
