###################################################################

# 脚本启动后可验证数据是否写入的地址：http://{http_server_addr}:{http_server_port}/metrics
#       如：http://localhost:7333/metrics

###################################################################

[base]
interval_sec = 1

[base.prometheus]
http_server_addr = "0.0.0.0"
http_server_port = 7333

[base.redis]
redis_host = "localhost"
redis_port = 6379
redis_password = ""

###################################################################

[metrics]

# 第一个 gauge
[metrics.gauge1]
name = "kenta_scheduling_status"
documentation = "Kenta scheduling status in redis TS."
label_names = ["status"]
sliding_window_sec = -1

[metrics.gauge1.redis]
ts_keys = [
    ["GRAFANA_TASK_STATUS_NEW", "new"],
    ["GRAFANA_TASK_STATUS_QUEUED", "queued"],
    ["GRAFANA_TASK_STATUS_LOCK", "lock"],
    ["GRAFANA_TASK_STATUS_PT", "pt"],
    ["GRAFANA_TASK_STATUS_PB", "pb"],
    ["GRAFANA_TASK_STATUS_MERGE", "merge"],
    ["GRAFANA_TASK_STATUS_TOOK", "took"]
]

# 第二个 gauge
[metrics.gauge2]
name = "kenta_scheduling_status_transform"
documentation = "Kenta scheduling status transform from redis TS to Prometheus.(aggregated by 60s)"
label_names = ["status"]
sliding_window_sec = 60

[metrics.gauge2.redis]
ts_keys = [
    ["GRAFANA_TASK_STATUS_NEW", "new"],
    ["GRAFANA_TASK_STATUS_QUEUED", "queued"],
    ["GRAFANA_TASK_STATUS_LOCK", "lock"],
    ["GRAFANA_TASK_STATUS_PT", "pt"],
    ["GRAFANA_TASK_STATUS_PB", "pb"],
    ["GRAFANA_TASK_STATUS_MERGE", "merge"],
    ["GRAFANA_TASK_STATUS_TOOK", "took"]
]

# 第三个 gauge
[metrics.gauge3]
name = "kenta_scheduling_status_transform_custom_sec"
documentation = "Kenta scheduling status transform from redis TS to Prometheus.(aggregated by custom sliding window sec)"
label_names = ["status"]
sliding_window_sec = 5

[metrics.gauge3.redis]
ts_keys = [
    ["GRAFANA_TASK_STATUS_NEW", "new"],
    ["GRAFANA_TASK_STATUS_QUEUED", "queued"],
    ["GRAFANA_TASK_STATUS_LOCK", "lock"],
    ["GRAFANA_TASK_STATUS_PT", "pt"],
    ["GRAFANA_TASK_STATUS_PB", "pb"],
    ["GRAFANA_TASK_STATUS_MERGE", "merge"],
    ["GRAFANA_TASK_STATUS_TOOK", "took"]
]

[metrics.rts]
source_key = "GRAFANA_NEOX_ENGINE_REQUEST_COUNT"
target_key = "GRAFANA_SDE_PRES_COUNT_TODAY"
compare_key = "GRAFANA_SDE_PRES_COUNT_LAST_WEEKDAY"
