# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['neox_metrics.py'],
    pathex=['.'],  # 添加当前目录到搜索路径
    binaries=[],
    datas=[],
    hiddenimports=['common.configuration'],  # 明确指定需要包含的隐藏导入模块
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='neox_metrics',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
