# -*- mode: python ; coding: utf-8 -*-

import os

a = Analysis(
    ['neox_metrics.py'],
    pathex=[os.path.abspath('.')],  # 使用绝对路径
    binaries=[],
    datas=[('common', 'common')],  # 将 common 目录作为数据文件包含
    hiddenimports=['common.configuration', 'common'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='neox_metrics',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
