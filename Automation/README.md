# Automation Specification Document

以下为自动化测试工程采用的目录组织结构说明文档。

## Directory Structure

```powershell
${project_dir}
│   conftest.py
│   pytest.ini
│   README.md
│   requirements.txt
│   runall.py
│
├───conf
├───logic
│       __init__.py
│
├───testsuite
└───utils
        __init__.py
```

整个目录结构如下，其中第一级目录和文件有：

- conf（目录）：主要用来存放项目运行环境、执行环境相关的配置参数
- logic（目录）：与项目有关的关键字、业务逻辑封装放到此目录
- report（目录）：生成 allure 测试报告的目录
- testsuite（目录）：测试用例在此目录编写，pytest 默认约定 test 开头的文件和方法为测试用例，不满足条件的不会被执行，内部建议按照特性建立文件夹对测试用例进行分类
- utils（目录）：把与业务无关的实用程序放到此目录，比如自己写的辅助方法
- conftest.py（文件）：pytes t的 fixture 方法可以写在这里，测试用例使用其中的 fixture 不需要使用 import 显示引入
- pytest.ini（文件）：可以针对 pytest 进行一些配置，如日志格式和级别等，后面会展示其配置内容
- runall.py（文件）：执行全部用例入口，主要用来给 CI/CD 工具拉起自动化任务使用
- README.md（文件）：自动化工程说明文档
- requirements.txt（文件）：当前项目工程额外所需安装的 python 第三方库写入此文件，如有该文件，则需要执行 `pip install -r requirements.txt` 一次性安装所需依赖

**注意：** 以上仅为标准项目的目录结构，可根据实际情况自行调整，如 logic 及 utils 包目录可单独抽出封装为外部包等

## Template

以下模板文件仅供参考，实际情况按需调整。

### pytest.ini

下面的配置主要是针对日志格式和级别进行了设置，`log_cli` 开头的配置是针对控制台输出设置的，`log_file` 开头的是针对输出到日志文件来设置的，会生成 `run.log` 日志文件，在 `./log` 目录下，如果 log 目录不存在会自动创建。

```ini
[pytest]
log_cli = 1
log_cli_level = INFO
log_cli_date_format = %Y-%m-%d-%H-%M-%S
log_cli_format = %(asctime)s - %(filename)s - %(module)s - %(funcName)s - %(lineno)d - %(levelname)s - %(message)s
log_file = ./log/run.log
log_file_level = INFO
log_file_date_format = %Y-%m-%d-%H-%M-%S
log_file_format = %(asctime)s - %(filename)s - %(module)s - %(funcName)s - %(lineno)d - %(levelname)s - %(message)s
```

### requirements.txt

列举依赖的python第三方库，可以指定版本，也可以不指定，不指定的情况下默认安装最新版本。文件示例内容如下，其中pytest、pytest-html、pytest-xdist 等 pytest相关的部分库是必须的，其他需要根据自己的项目情况来定。

```bash
jmespath==1.0.1
loguru==0.7.2
pendulum==3.0.0
PyAutoGUI==0.9.54
python-dotenv==1.0.1
PyYAML==6.0.1
toml==0.10.2

pip-autoremove
pipdeptree
```

### runall.py

runall.py 文件主要是给 CI/CD 工具拉起自动化任务使用的（如 jenkins 等），它的主要作用就是执行 `pytest.main` 方法来使用 pytest 框架来收集并执行测试用例，最终会生成 html 和 xml 两份报告。

```python
import os
import time
import pytest

from conf.settings import *

def new_report_dir():
    return time.strftime("%Y-%m-%d")

def new_report_name(project="demo", file_type="html"):
    """file_type：文件后缀名，默认为html
    """
    now = time.strftime("%Y-%m-%d_%H-%M-%S")
    report_name = "{2}_{0}.{1}".format(now, file_type, project)
    return report_name

if __name__ == "__main__":
    report_base_dir = r".\report"
    report_html = os.path.join(report_base_dir, new_report_dir(), new_report_name())
    report_xml = os.path.join(report_base_dir, new_report_dir(), new_report_name("demo", "xml"))
    pytest.main(["--html={0}".format(report_html), 
                "--junit-xml={0}".format(report_xml), 
                "-n {0}".format(CPU_NUM),
                "--dist=loadfile"])
```

当然也可以使用该文件集成 allure 测试报告。
