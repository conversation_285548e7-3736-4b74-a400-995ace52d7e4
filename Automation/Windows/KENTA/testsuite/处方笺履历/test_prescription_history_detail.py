# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-07
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   test_prescription_history_detail.py.py
@Software   :   PyCharm
"""

import allure
from neox_test_common import logger


@allure.epic("薬師丸賢太")
@allure.suite("处方笺履历")
@allure.sub_suite("展示处方笺履历详细")
@allure.feature("处方笺履历")
@allure.story("展示处方笺履历详细")
@allure.title("测试用例：展示处方笺履历详细")
def test_prescription_history_display_detail(config):
    """
    TestCase: 处方笺履历 - 展示处方笺履历详细
    """
    with allure.step("展示处方笺履历详细"):
        logger.info("< Test :: 展示处方笺履历详细 >")
        assert True
