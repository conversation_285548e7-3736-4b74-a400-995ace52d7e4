# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-07
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   test_prescription_history_fault.py
@Software   :   PyCharm
"""

import allure
from neox_test_common import logger


@allure.epic("薬師丸賢太")
@allure.suite("处方笺履历")
@allure.sub_suite("提交不具合报告")
@allure.feature("处方笺履历")
@allure.story("提交不具合报告")
@allure.title("测试用例：提交不具合报告")
def test_prescription_history_submit_fault_report(config):
    """
    TestCase: 处方笺履历 - 提交不具合报告
    """
    with allure.step("提交不具合报告"):
        logger.info("< Test :: 提交不具合报告 >")
        assert True
