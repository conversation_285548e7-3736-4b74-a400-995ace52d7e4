# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
.Python
build
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# file lock
*.lock

# Custom
.DS_Store
.idea/
.fleet/
.vscode/
report/
temp/
test.py
test[0-9].py
test[0-9][0-9].py

# extend file type
*.log
*.pdf
*.ini

# NeoX
jpeg/
