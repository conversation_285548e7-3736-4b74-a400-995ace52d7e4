# This file configures the 'uv' tool.
# uv discovers this file in the current directory or any parent directory.
# For more details, see the uv documentation: https://docs.astral.sh/uv/reference/settings/

[pip]
# The URL of the Python package index (by default: https://pypi.org/simple).
# Set the package registry index URL.
# Using a regional mirror can speed up dependency installation.
index-url = "https://mirrors.aliyun.com/pypi/simple"

# The minimum Python version that should be supported by the resolved requirements.
python-version = "3.12"

# Set the Python interpreter for this project.
# uv will use this interpreter to create virtual environments.
python = "/Users/<USER>/miniconda3/envs/locust_v2.37.10/bin/python"
