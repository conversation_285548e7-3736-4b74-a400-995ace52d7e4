# Configuration Instructions

## Prerequisites

```bash
sudo /root/miniconda3/envs/locust_v2.32.5/bin/python -m pip install sanic
# or
sudo /root/miniconda3/envs/locust_v2.32.5/bin/python -m pip install -r */env/requirements/locust_backend_requirements.txt

# and
sudo /root/miniconda3/envs/locust_v2.32.5/bin/python -m pip install */env/lib/locust_common*
sudo /root/miniconda3/envs/locust_v2.32.5/bin/python -m pip install */env/lib/neox_locust*
```

## Using systemd

Create neox-locust-backend.service & log files

```bash
sudo touch /etc/systemd/system/neox-locust-backend.service
sudo touch /var/log/neox-locust-backend.access.log
sudo touch /var/log/neox-locust-backend.error.log

sudo chown ubuntu:ubuntu /var/log/neox-locust-backend.*
```

Input context like:

```ini
[Unit]
Description=Sanic web application for neox-locust-backend
After=network.target

[Service]
User=ubuntu
WorkingDirectory=/home/<USER>/docker-compose/locust/NeoX/backend
ExecStart=sudo /root/miniconda3/envs/locust_v2.32.5/bin/python /home/<USER>/docker-compose/locust/NeoX/backend/neox-locust-backend.py
ExecStop=/usr/bin/kill -SIGTERM $MAINPID
Restart=always
RestartSec=1
StandardOutput=file:/var/log/neox-locust-backend.access.log
StandardError=file:/var/log/neox-locust-backend.error.log

[Install]
WantedBy=multi-user.target
```


## Managing service

```bash
sudo systemctl start neox-locust-backend.service

# Make service run on startup
sudo systemctl enable neox-locust-backend.service

sudo systemctl status neox-locust-backend.service

sudo systemctl restart neox-locust-backend.service

sudo systemctl stop neox-locust-backend.service
```

## Logs

```bash
sudo tail -f /var/log/neox-locust-backend.access.log
sudo tail -f /var/log/neox-locust-backend.error.log
```

## Verification

```bash
curl -X GET http://localhost:9333/locust/config/get
```
